// px to vw 转换配置文件
// 基于1920*1080设计稿的响应式配置

export const pxToVwConfig = {
  // 基础配置
  viewportWidth: 1920,        // 设计稿宽度
  viewportHeight: 1080,       // 设计稿高度
  unitPrecision: 5,           // 转换后保留的小数位数
  viewportUnit: 'vw',         // 转换后的单位
  minPixelValue: 1,           // 小于或等于1px不转换
  mediaQuery: false,          // 不在媒体查询中转换px
  replace: true,              // 直接替换属性值

  // 排除规则 - 不需要转换的选择器
  selectorBlackList: [
    '.ignore',              // 忽略包含ignore类的元素
    '.hairlines',           // 忽略细线类
    '.el-',                 // 忽略Element Plus组件
    '.echarts',             // 忽略ECharts相关
    // '[data-v-',          // 可选：忽略Vue scoped样式
  ],

  // 文件排除规则
  exclude: [
    /node_modules/,         // 排除node_modules
    /\.min\./,              // 排除压缩文件
  ],

  // 横屏配置（可选）
  landscape: false,
  landscapeUnit: 'vw',
  landscapeWidth: 1920,

  // 自定义转换函数（高级配置）
  propList: ['*'],          // 需要转换的CSS属性，* 表示所有
}

// 针对不同屏幕尺寸的预设配置
export const screenPresets = {
  // 1920*1080 (主要设计稿)
  desktop: {
    viewportWidth: 1920,
    viewportHeight: 1080,
  },

  // 1366*768 (常见笔记本)
  laptop: {
    viewportWidth: 1366,
    viewportHeight: 768,
  },

  // 2560*1440 (2K显示器)
  desktop2k: {
    viewportWidth: 2560,
    viewportHeight: 1440,
  },

  // 3840*2160 (4K显示器)
  desktop4k: {
    viewportWidth: 3840,
    viewportHeight: 2160,
  }
}

// 导出默认配置
export default pxToVwConfig
