#!/usr/bin/env node

/**
 * 响应式配置检查脚本
 * 用于验证px to vw配置是否正确设置
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🔍 检查响应式配置...\n')

// 检查项目列表
const checks = [
  {
    name: '检查 postcss-px-to-viewport-8-plugin 是否安装',
    check: () => {
      try {
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
        return packageJson.devDependencies && packageJson.devDependencies['postcss-px-to-viewport-8-plugin']
      } catch (error) {
        return false
      }
    }
  },
  {
    name: '检查 postcss.config.js 配置',
    check: () => {
      try {
        const configExists = fs.existsSync('postcss.config.js')
        if (!configExists) return false
        
        const configContent = fs.readFileSync('postcss.config.js', 'utf8')
        return configContent.includes('postcss-px-to-viewport-8-plugin') && 
               configContent.includes('px-to-vw.config.js')
      } catch (error) {
        return false
      }
    }
  },
  {
    name: '检查 px-to-vw.config.js 配置文件',
    check: () => {
      try {
        return fs.existsSync('px-to-vw.config.js')
      } catch (error) {
        return false
      }
    }
  },
  {
    name: '检查响应式工具类',
    check: () => {
      try {
        return fs.existsSync('src/utils/responsive.js')
      } catch (error) {
        return false
      }
    }
  },
  {
    name: '检查测试页面',
    check: () => {
      try {
        return fs.existsSync('src/views/ResponsiveTest.vue')
      } catch (error) {
        return false
      }
    }
  },
  {
    name: '检查路由配置',
    check: () => {
      try {
        const routerContent = fs.readFileSync('src/router/index.js', 'utf8')
        return routerContent.includes('ResponsiveTest')
      } catch (error) {
        return false
      }
    }
  }
]

// 执行检查
let passedChecks = 0
const totalChecks = checks.length

checks.forEach((check, index) => {
  const result = check.check()
  const status = result ? '✅' : '❌'
  const number = `${index + 1}`.padStart(2, '0')
  
  console.log(`${status} ${number}. ${check.name}`)
  
  if (result) {
    passedChecks++
  }
})

console.log('\n' + '='.repeat(50))
console.log(`检查完成: ${passedChecks}/${totalChecks} 项通过`)

if (passedChecks === totalChecks) {
  console.log('🎉 所有配置检查通过！')
  console.log('\n📋 下一步操作:')
  console.log('1. 启动开发服务器: npm run dev')
  console.log('2. 访问测试页面: http://localhost:5173/#/responsive-test')
  console.log('3. 使用浏览器开发者工具测试不同屏幕尺寸')
  console.log('4. 检查现有页面的响应式效果')
} else {
  console.log('⚠️  部分配置未完成，请检查失败的项目')
  console.log('\n🔧 修复建议:')
  
  if (!checks[0].check()) {
    console.log('- 安装插件: npm install postcss-px-to-viewport-8-plugin --save-dev')
  }
  
  if (!checks[1].check()) {
    console.log('- 检查 postcss.config.js 配置是否正确')
  }
  
  if (!checks[2].check()) {
    console.log('- 确保 px-to-vw.config.js 文件存在')
  }
  
  if (!checks[3].check()) {
    console.log('- 确保 src/utils/responsive.js 文件存在')
  }
  
  if (!checks[4].check()) {
    console.log('- 确保 src/views/ResponsiveTest.vue 文件存在')
  }
  
  if (!checks[5].check()) {
    console.log('- 检查路由配置是否包含测试页面')
  }
}

console.log('\n📚 相关文档:')
console.log('- 响应式适配指南: RESPONSIVE_GUIDE.md')
console.log('- 实施示例: IMPLEMENTATION_EXAMPLE.md')
console.log('- 配置文件: px-to-vw.config.js')

// 显示当前配置信息
try {
  const { pxToVwConfig } = await import('./px-to-vw.config.js')
  console.log('\n⚙️  当前配置:')
  console.log(`- 设计稿宽度: ${pxToVwConfig.viewportWidth}px`)
  console.log(`- 设计稿高度: ${pxToVwConfig.viewportHeight}px`)
  console.log(`- 最小转换值: ${pxToVwConfig.minPixelValue}px`)
  console.log(`- 转换精度: ${pxToVwConfig.unitPrecision}位小数`)
} catch (error) {
  console.log('\n⚠️  无法读取配置信息')
}
