<template>
  <div class="gas-monitor-container">
    <!-- 头部区域 -->
    <div class="header-section">
      <div class="left-section">
        <div class="realtime-label">实时</div>
        <div class="date-picker-wrapper">
          <el-date-picker v-model="selectedDate" type="date" placeholder="选择日期" format="YYYY.MM.DD"
            value-format="YYYY-MM-DD" @change="handleDateChange" class="custom-date-picker" />
        </div>
      </div>
    </div>

    <!-- 数据展示区 -->
    <div class="data-display-section">
      <!-- CO仪表盘 -->
      <div class="gauge-container">
        <div ref="coGaugeRef" class="gauge-chart"></div>
        <div class="gauge-label">CO</div>
      </div>

      <!-- CO₂仪表盘 -->
      <div class="gauge-container">
        <div ref="co2GaugeRef" class="gauge-chart"></div>
        <div class="gauge-label">CO₂</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import { ElDatePicker } from 'element-plus'

// Props
const props = defineProps({
  // CO数值
  coValue: {
    type: Number,
    default: 12
  },
  // CO₂数值
  co2Value: {
    type: Number,
    default: 36
  },
  // 初始日期
  initialDate: {
    type: String,
    default: '2025-07-01'
  }
})

// Emits
const emit = defineEmits(['dateChange', 'dataUpdate'])

// 响应式数据
const selectedDate = ref(props.initialDate)
const coGaugeRef = ref(null)
const co2GaugeRef = ref(null)
const coGaugeInstance = ref(null)
const co2GaugeInstance = ref(null)

// 处理日期变化
const handleDateChange = (date) => {
  emit('dateChange', date)
}

// 创建仪表盘配置 - 基于dashboard.html的样式
const createGaugeOption = (value, name, maxValue) => {
  // 计算填充百分比
  const percentage = (value / maxValue) * 100

  return {
    backgroundColor: 'transparent',
    gr
    series: [
      {
        // 外圈背景刻度线（黑色）
        type: 'gauge',
        startAngle: 220,
        endAngle: -40,
        min: 0,
        max: maxValue,
        splitNumber: 100,
        radius: '85%',
        center: ['50%', '50%'],
        axisLine: {
          show: false
        },
        axisTick: {
          show: true,
          length: 16,
          lineStyle: {
            color: '#000000',
            width: 1
          },
          splitNumber: 1
        },
        axisLabel: {
          show: false
        },
        splitLine: {
          show: false
        },
        pointer: {
          show: false
        },
        detail: {
          show: false
        }
      },
      {
        // 外圈填充刻度线（蓝色）
        type: 'gauge',
        startAngle: 220,
        endAngle: 220 - (260 * percentage / 100), // 根据数值计算结束角度
        min: 0,
        max: value, // 最大值设为当前值，这样所有刻度都会显示为蓝色
        splitNumber: Math.floor(100 * percentage / 100),
        radius: '85%',
        center: ['50%', '50%'],
        axisLine: {
          show: false
        },
        axisTick: {
          show: true,
          length: 16,
          lineStyle: {
            color: '#4A90E2',
            width: 1
          },
          splitNumber: 1
        },
        axisLabel: {
          show: false
        },
        splitLine: {
          show: false
        },
        pointer: {
          show: false
        },
        detail: {
          show: false
        }
      },
      {
        // 内圈背景刻度点（黑色）
        type: 'gauge',
        startAngle: 220,
        endAngle: -40,
        min: 0,
        max: maxValue,
        splitNumber: 100,
        radius: '70%',
        center: ['50%', '50%'],
        axisLine: {
          show: false
        },
        axisTick: {
          show: true,
          length: 2,
          lineStyle: {
            color: '#000000',
            width: 2
          },
          splitNumber: 1
        },
        axisLabel: {
          show: false
        },
        splitLine: {
          show: false
        },
        pointer: {
          show: false
        },
        detail: {
          show: false
        }
      },
      {
        // 内圈填充刻度点（蓝色）
        type: 'gauge',
        startAngle: 220,
        endAngle: 220 - (260 * percentage / 100), // 根据数值计算结束角度
        min: 0,
        max: value, // 最大值设为当前值
        splitNumber: Math.floor(100 * percentage / 100),
        radius: '70%',
        center: ['50%', '50%'],
        axisLine: {
          show: false
        },
        axisTick: {
          show: true,
          length: 2,
          lineStyle: {
            color: '#4A90E2',
            width: 2,
            //线段圆角为1px
            borderRadius: 1
          },
          splitNumber: 1
        },
        axisLabel: {
          show: false
        },
        splitLine: {
          show: false
        },
        pointer: {
          show: false
        },
        detail: {
          show: false
        }
      },
      {
        // 中心数值显示
        type: 'gauge',
        startAngle: 220,
        endAngle: -40,
        min: 0,
        max: maxValue,
        radius: '60%',
        center: ['50%', '50%'],
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        },
        splitLine: {
          show: false
        },
        pointer: {
          show: false
        },
        detail: {
          show: true,
          valueAnimation: true,
          formatter: '{value}',
          fontSize: 48,
          fontWeight: 'bold',
          color: '#4A90E2',
          offsetCenter: [0, 0]
        },
        data: [{ value: value }]
      }
    ]
  }
}

// 初始化仪表盘
const initGauges = () => {
  if (coGaugeRef.value) {
    coGaugeInstance.value = echarts.init(coGaugeRef.value)
    const coOption = createGaugeOption(props.coValue, 'CO', 100)
    coGaugeInstance.value.setOption(coOption)
  }

  if (co2GaugeRef.value) {
    co2GaugeInstance.value = echarts.init(co2GaugeRef.value)
    const co2Option = createGaugeOption(props.co2Value, 'CO₂', 100)
    co2GaugeInstance.value.setOption(co2Option)
  }
}

// 更新仪表盘数据
const updateGauges = () => {
  if (coGaugeInstance.value) {
    const coOption = createGaugeOption(props.coValue, 'CO', 100)
    coGaugeInstance.value.setOption(coOption)
  }

  if (co2GaugeInstance.value) {
    const co2Option = createGaugeOption(props.co2Value, 'CO₂', 100)
    co2GaugeInstance.value.setOption(co2Option)
  }
}

// 监听窗口大小变化
const handleResize = () => {
  if (coGaugeInstance.value) {
    coGaugeInstance.value.resize()
  }
  if (co2GaugeInstance.value) {
    co2GaugeInstance.value.resize()
  }
}

// 监听props变化
watch(() => [props.coValue, props.co2Value], () => {
  updateGauges()
}, { deep: true })

watch(() => props.initialDate, (newDate) => {
  selectedDate.value = newDate
})

// 生命周期
onMounted(() => {
  nextTick(() => {
    initGauges()
    window.addEventListener('resize', handleResize)
  })
})

onUnmounted(() => {
  if (coGaugeInstance.value) {
    coGaugeInstance.value.dispose()
  }
  if (co2GaugeInstance.value) {
    co2GaugeInstance.value.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.gas-monitor-container {
  width: 100%;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// 头部区域
.header-section {
  margin-bottom: 10px;
  padding-bottom: 15px;
}

.left-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.realtime-label {
  color: #333333;
  font-size: 18px;
  font-weight: 600;
}

.date-picker-wrapper {
  display: flex;
  align-items: center;
}

.custom-date-picker {
  width: 150px;

  :deep(.el-input__wrapper) {
    background: #ffffff;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    box-shadow: none;

    &:hover {
      border-color: #4A90E2;
    }

    &.is-focus {
      border-color: #4A90E2;
    }
  }

  :deep(.el-input__inner) {
    color: #333333;
    font-size: 14px;

    &::placeholder {
      color: #a8abb2;
    }
  }
}

// 数据展示区
.data-display-section {
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 40px;
  padding: 20px 0;
}

.gauge-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.gauge-chart {
  width: 300px;
  height: 300px;
}

.gauge-label {
  font-size: 36px;
  font-weight: 600;
  color: #333333;
  text-align: center;
  margin-top: -100px;
}


// 响应式设计
@media (max-width: 1200px) {
  .data-display-section {
    gap: 20px;
  }

  .gauge-chart {
    width: 250px;
    height: 250px;
  }

  .gauge-label {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .data-display-section {
    flex-direction: column;
    gap: 30px;
  }

  .gauge-chart {
    width: 200px;
    height: 200px;
  }

  .gauge-label {
    font-size: 18px;
  }

  .header-section {
    .left-section {
      flex-direction: column;
      gap: 10px;
      align-items: flex-start;
    }
  }
}
</style>
