<template>
    <div class="index-container">
        <TopNav />
        <div class="main-content">
            <SideNav @nav-click="handleNavClick" />
            <div class="content">
                <ContentTabs ref="contentTabsRef" />
                <div class="content-container">
                    <div class="chart">
                        <LineChartComponent :indicator-data="indicatorData" :chart-data="chartData"
                            @time-range-change="handleTimeRangeChange" @data-update="handleDataUpdate" />
                    </div>
                    <div class="chart">
                        <div class="item">
                            <ColorBandComponent :pm-value="pmData.value" :initial-date="pmData.date"
                                @date-change="handlePmDateChange" @value-update="handlePmValueUpdate" />
                        </div>
                        <div class="item">
                            <ColorBandComponent :pm-value="pmData.value" :initial-date="pmData.date"
                                @date-change="handlePmDateChange" @value-update="handlePmValueUpdate" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import TopNav from '../components/TopNav.vue';
import SideNav from '@/components/SideNav.vue';
import ContentTabs from '@/components/ContentTabs.vue';
import LineChartComponent from '@/components/LineChartComponent.vue';
import ColorBandComponent from '@/components/ColorBandComponent.vue';

// 组件引用
const contentTabsRef = ref(null)

// 指标数据
const indicatorData = reactive({
    radiation: 0.552,
})

// 图表数据
const chartData = reactive({
    times: ['18:00', '18:01', '18:02', '18:03', '18:04', '18:05', '18:06', '18:07', '18:08', '18:09'],
    values: [25.6, 26.1, 25.8, 26.3, 25.9, 26.0, 25.8, 26.3, 25.8, 26.3]
})

// PM₁₀数据
const pmData = reactive({
    value: 88,
    date: '2025-07-01'
})

// 处理侧边栏导航点击
const handleNavClick = ({ index }) => {
    // 切换ContentTabs中的机构信息
    if (contentTabsRef.value) {
        contentTabsRef.value.switchInstitution(index)
    }
}

// 处理时间范围变化
const handleTimeRangeChange = (timeRange) => {
    console.log('时间范围变化:', timeRange)
    // 这里可以根据时间范围请求新的数据
    // 示例：更新指标数据
    updateIndicatorData(timeRange)
}

// 处理数据更新
const handleDataUpdate = (data) => {
    console.log('数据更新:', data)
}

// 处理PM日期变化
const handlePmDateChange = (date) => {
    console.log('PM日期变化:', date)
    pmData.date = date
    // 这里可以根据日期请求新的PM数据
    handlePmValueUpdate()
}

// 处理PM数值更新
const handlePmValueUpdate = (value) => {
    console.log('PM数值更新:', value)
    pmData.value = Math.floor(Math.random() * 301);
}

const updateIndicatorData = (timeRange) => {
    // 根据时间范围生成不同的模拟数据
    indicatorData.radiation = Number((0.12 + Math.random() * 0.08 - 0.04).toFixed(3))
}
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.index-container {
    width: 100%;
    height: 100%;
    background: url(../assets/img/background.png);
    background-size: cover;
    background-repeat: no-repeat;
}

.main-content {
    display: flex;
    height: calc(100vh - 60px);
}

.content {
    width: calc(100vw - 350px);
    height: calc(100vh - 60px);
    // overflow: hidden;
}

.content-container {
    width: 100%;
    overflow: hidden;
    background-color: rgba(12, 25, 44, 0.86);
    padding: 30px 40px;
    display: flex;
    flex-direction: column;
}

.contentImg {
    width: calc(100% - 100px);
    height: auto;
    display: block;
    object-fit: contain;
    margin: 0 auto;
}

.chart {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.item {
    margin-top: 15px;
    width: 49.5%;
}
</style>