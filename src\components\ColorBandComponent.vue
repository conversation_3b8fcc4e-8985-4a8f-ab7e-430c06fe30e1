<template>
  <div class="color-band-container">
    <!-- 顶部区域栏 -->
    <div class="header-section">
      <div class="left-section">
        <div class="realtime-label">实时</div>
        <div class="date-picker-wrapper">
          <el-date-picker
            v-model="selectedDate"
            type="date"
            placeholder="选择日期"
            format="YYYY.MM.DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
            class="custom-date-picker"
          />
        </div>
      </div>
    </div>

    <!-- 数据展示区 -->
    <div class="data-display-section">
      <!-- 左侧蓝色方块 -->
      <div class="pm-value-block">
        <div class="pm-value">{{ pmValue }}</div>
        <div class="pm-label">PM₁₀</div>
      </div>

      <!-- 右侧分段色带 -->
      <div class="color-band-wrapper">
        <!-- 当前数值对应位置的对话泡泡框 -->
        <div
          class="value-bubble"
          :style="{
            left: currentValuePosition + '%',
            backgroundColor: currentSegmentColor
          }"
        >
          <div class="bubble-content">{{ currentLevelText }}</div>
          <div class="bubble-arrow"  :style="{borderTopColor: currentSegmentColor}"></div>
        </div>

        <div class="color-band">
          <div
            v-for="(segment, index) in colorSegments"
            :key="index"
            :class="['band-segment', { active: isActiveSegment(segment) }]"
            :style="{
              backgroundColor: segment.color,
              width: segment.width + '%'
            }"
          >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElDatePicker } from 'element-plus'

// Props
const props = defineProps({
  // PM₁₀ 数值
  pmValue: {
    type: Number,
    default: 32
  },
  // 初始日期
  initialDate: {
    type: String,
    default: '2025-07-01'
  },
  // 自定义色带配置
  customSegments: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['dateChange', 'valueUpdate'])

// 响应式数据
const selectedDate = ref(props.initialDate)

// 默认色带配置（符合空气质量标准）
const defaultColorSegments = [
  { min: 0, max: 50, color: '#62BB5F', level: '优', width: 16.67 },
  { min: 51, max: 100, color: '#F6CE03', level: '良', width: 16.67 },
  { min: 101, max: 150, color: '#F8A022', level: '轻度污染', width: 16.67 },
  { min: 151, max: 200, color: '#EA7025', level: '中度污染', width: 16.67 },
  { min: 201, max: 250, color: '#E14663', level: '重度污染', width: 16.67 },
  { min: 251, max: 300, color: '#AA49EE', level: '严重污染', width: 16.67 }
]

// 色带配置
const colorSegments = computed(() => {
  return props.customSegments.length > 0 ? props.customSegments : defaultColorSegments
})

// 计算当前数值在色带中的位置百分比
const currentValuePosition = computed(() => {
  let position = 0
  for (const segment of colorSegments.value) {
    if (props.pmValue >= segment.min && props.pmValue <= segment.max) {
      // 在当前段内的相对位置
      const segmentProgress = (props.pmValue - segment.min) / (segment.max - segment.min)
      return position + (segmentProgress * segment.width)
    }
    position += segment.width
  }
  return 0 // 如果没有匹配的段，返回0
})

// 获取当前数值对应的颜色
const currentSegmentColor = computed(() => {
  const activeSegment = colorSegments.value.find(segment =>
    props.pmValue >= segment.min && props.pmValue <= segment.max
  )
  return activeSegment ? activeSegment.color : '#cccccc'
})

// 获取当前数值对应的等级文字
const currentLevelText = computed(() => {
  const activeSegment = colorSegments.value.find(segment =>
    props.pmValue >= segment.min && props.pmValue <= segment.max
  )
  return activeSegment ? activeSegment.level : '未知'
})

// 判断是否为当前活跃的色带段
const isActiveSegment = (segment) => {
  return props.pmValue >= segment.min && props.pmValue <= segment.max
}

// 格式化日期显示
const formatDate = (dateStr) => {
  if (!dateStr) return '2025.07.01'
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}.${month}.${day}`
}

// 处理日期变化
const handleDateChange = () => {
  emit('dateChange', selectedDate.value)
}

// 监听props变化
watch(() => props.pmValue, (newValue) => {
  emit('valueUpdate', newValue)
})

watch(() => props.initialDate, (newDate) => {
  selectedDate.value = newDate
})
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.color-band-container {
  width: 100%;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 20px;
  // margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// 顶部区域栏
.header-section {
  margin-bottom: 10px;
  padding-bottom: 15px;
}

.left-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.realtime-label {
  color: #1890FF;
  font-size: 18px;
  font-weight: 600;
}

.date-picker-wrapper {
  display: flex;
  align-items: center;
}

.custom-date-picker {
  width: 150px;

  :deep(.el-input__wrapper) {
    background: #ffffff;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    box-shadow: none;

    &:hover {
      border-color: #4A90E2;
    }

    &.is-focus {
      border-color: #4A90E2;
    }
  }

  :deep(.el-input__inner) {
    color: #333333;
    font-size: 14px;

    &::placeholder {
      color: #a8abb2;
    }
  }
}

// 数据展示区
.data-display-section {
  display: flex;
  align-items: center;
  gap: 30px;
}

// 左侧PM₁₀数值方块
.pm-value-block {
  background: #1890FF;
  border-radius: 5px;
  padding: 20px;
  text-align: center;
  min-width: 125px;
  min-height: 125px;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.pm-value {
  color: #ffffff;
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 8px;
}

.pm-label {
  color: #ffffff;
  font-size: 18px;
  font-weight: 500;
}

// 右侧色带
.color-band-wrapper {
  flex: 1;
  position: relative;
}

.color-band {
  display: flex;
  height: 40px;
  border-radius: 23px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  position: relative;
  margin-top: 40px; // 为泡泡框留出空间
}

.band-segment {
  position: relative;
  transition: all 0.3s ease;

  &.active {
    // box-shadow: inset 0 0 0 2px #ffffff;
    z-index: 2;
  }
}

// 当前数值对应位置的对话泡泡框
.value-bubble {
  position: absolute;
  top: 0px;
  transform: translateX(-50%);
  z-index: 10;
  transition: all 0.3s ease;
  border-radius: 19px 19px 19px 19px;
  border: 0px solid #040404;
}

.bubble-content {
  color: #ffffff;
  padding: 6px 12px;
  font-size: 16px;
  font-weight: 500;
  white-space: nowrap;
  min-width: 60px;
  height: 30px;
  text-align: center;
  line-height: 18px;
}

.bubble-arrow {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid transparent;
}

// 响应式设计
@media (max-width: 768px) {
  .data-display-section {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .pm-value-block {
    align-self: center;
    min-width: 100px;
  }

  .pm-value {
    font-size: 28px;
  }

  .pm-label {
    font-size: 14px;
  }

  .color-band {
    height: 35px;
  }

  .level-label {
    font-size: 11px;
    padding: 3px 6px;
  }

  .header-section {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
</style>
