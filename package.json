{"name": "health-supervision", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"echarts": "^5.6.0", "element-plus": "^2.10.2", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "globals": "^16.0.0", "postcss": "^8.5.4", "postcss-px-to-viewport-8-plugin": "^1.2.5", "prettier": "3.5.3", "sass": "^1.83.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}