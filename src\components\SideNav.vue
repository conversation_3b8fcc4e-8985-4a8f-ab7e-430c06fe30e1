<template>
    <aside class="side-nav" :class="{ 'side-nav--collapsed': !isExpanded }">
        <!-- 搜索框 -->
        <div class="search-container">
            <div class="search-box">
                <svg class="search-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <input type="text" class="search-input" placeholder="请输入" v-model="searchQuery" />
            </div>
        </div>

        <!-- 导航条目 -->
        <div class="nav-container">
            <div v-for="(item, index) in navItems" :key="index" class="nav-item"
                :class="{ 'nav-item--active': index === activeIndex, 'nav-item--transparent': index !== activeIndex }"
                @click="handleNavClick(index)">
                <span class="nav-text">{{ item.name }}</span>
            </div>
        </div>
    </aside>
</template>

<script setup>
import { ref, defineProps, defineEmits, computed } from 'vue'
import { useRoute } from 'vue-router'

// Props
const props = defineProps({
    isExpanded: {
        type: Boolean,
        default: true
    }
})

// Emits
const emit = defineEmits(['nav-click', 'search'])

// 路由
const route = useRoute()

// 响应式数据
const searchQuery = ref('')
const activeIndex = ref(0)

// 根据路由配置不同页面的导航项
const pageNavConfigs = {
    'PublicPlaceMonitor': [
        { name: '漳平市蓝方娱乐会所', id: 'place1' },
        { name: '漳平市心满意足养生馆', id: 'place2' }
    ],
    'RadiationHealthMonitor': [
        { name: '放射医院1', id: 'hospital1' },
        { name: '放射医院2', id: 'hospital2' }
    ],
    'OccupationalHazardMonitor': [
        { name: '职业卫生公司一', id: 'company1' },
        { name: '职业卫生公司二', id: 'company2' }
    ],
    'RealTimeVideoMonitor': [
        { name: '漳平市和平镇卫生院', id: 'clinic1' },
        { name: '漳平市溪南镇南柄村第一卫生室', id: 'clinic2' }
    ]
}

// 当前页面的导航项
const navItems = computed(() => {
    return pageNavConfigs[route.name] || pageNavConfigs['PublicPlaceMonitor']
})

// 方法
const handleNavClick = (index) => {
    activeIndex.value = index
    emit('nav-click', { index, item: navItems.value[index] })
}
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.side-nav {
    width: $sidenav-width;
    height: calc(100vh - 60px);
    background: linear-gradient(to right, RGBA(28, 72, 160, 1), 20%, RGBA(11, 35, 85, 1), rgba(12, 25, 44, 0.86));
    padding: $spacing-lg 0;
    transition: width 0.3s ease;
    overflow: hidden;
    margin-right: 20px;

    &--collapsed {
        width: 0px;
        margin-left: 0px;
    }
}

// 搜索框容器
.search-container {
    margin-bottom: $spacing-lg;
    display: flex;
    justify-content: center;
}

.search-box {
    position: relative;
    width: calc(100% - 32px);
    height: $search-box-height;
    background: $bg-primary;
    border-radius: 23px;
    @include flex-center;
    padding: 0 $spacing-sm;
    box-shadow: $shadow-sm;
}

.search-icon {
    width: 20px;
    height: 20px;
    color: #003984;
    margin-right: $spacing-sm;
    flex-shrink: 0;

}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: 16px;
    color: #003984;

    &::placeholder {
        color: #003984;
    }
}

// 导航容器
.nav-container {
    display: flex;
    flex-direction: column;
    gap: $spacing-sm;
}

.nav-item {
    height: $nav-item-height;
    @include flex-center;
    // border-radius: $border-radius-md;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0 $spacing-md;

    &--active {
        background: $blue-primary;

        .nav-text {
            color: $text-white;
        }
    }

    &--transparent {
        background: transparent;

        .nav-text {
            color: $text-white;
        }

        &:hover {
            background: rgba(255, 255, 255, 0.1);
        }
    }
}

.nav-text {
    font-size: 18px;
    text-align: center;
    line-height: 1.4;
    word-break: break-all;
}

// 响应式适配
@media (max-width: 768px) {
    .side-nav {
        width: 280px;

        &--collapsed {
            width: 0;
        }
    }
}
</style>