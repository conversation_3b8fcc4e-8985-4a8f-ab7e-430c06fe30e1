<template>
    <div class="index-container">
        <TopNav />
        <div class="main-content">
            <div class="content">
                <div class="content-container">
                    <button class="add-device-btn" @click="toAddDevice()">添加设备</button>
                    <table class="device-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>设备编号</th>
                                <th>设备类型</th>
                                <th>绑定单位类型</th>
                                <th>绑定单位名称</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(item, index) in tableData" :key="index">
                                <td>{{ index + 1 }}</td>
                                <td>{{ item.deviceId }}</td>
                                <td>{{ item.deviceType }}</td>
                                <td>{{ item.bindingUnitType }}</td>
                                <td>{{ item.bindingUnitName }}</td>
                                <td>
                                    <router-link style="color: #000;" to="/add-device">编辑</router-link>
                                    <span style="margin-left: 8px;">删除</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import TopNav from '../components/TopNav.vue';

const router = useRouter()

const toAddDevice = () => {
    router.push('/add-device')
}

const tableData = ref([
    { deviceId: 'V15200', deviceType: '噪音设备', bindingUnitType: '公共场所', bindingUnitName: '公共场所1', operation: '编辑 删除' },
    { deviceId: 'V15200', deviceType: '噪音设备', bindingUnitType: '公共场所', bindingUnitName: '公共场所1', operation: '编辑 删除' },
    { deviceId: 'V15200', deviceType: '噪音设备', bindingUnitType: '公共场所', bindingUnitName: '公共场所1', operation: '编辑 删除' },
    { deviceId: 'V15200', deviceType: '噪音设备', bindingUnitType: '公共场所', bindingUnitName: '公共场所1', operation: '编辑 删除' },
    { deviceId: 'V15200', deviceType: '噪音设备', bindingUnitType: '公共场所', bindingUnitName: '公共场所1', operation: '编辑 删除' },
    { deviceId: 'V15200', deviceType: '噪音设备', bindingUnitType: '公共场所', bindingUnitName: '公共场所1', operation: '编辑 删除' },
    { deviceId: 'V15200', deviceType: '噪音设备', bindingUnitType: '公共场所', bindingUnitName: '公共场所1', operation: '编辑 删除' },
    { deviceId: 'V15200', deviceType: '噪音设备', bindingUnitType: '公共场所', bindingUnitName: '公共场所1', operation: '编辑 删除' },
    { deviceId: 'V15200', deviceType: '噪音设备', bindingUnitType: '公共场所', bindingUnitName: '公共场所1', operation: '编辑 删除' },
    { deviceId: 'V15200', deviceType: '噪音设备', bindingUnitType: '公共场所', bindingUnitName: '公共场所1', operation: '编辑 删除' }
]);
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.index-container {
    width: 100%;
    height: 100%;
    background: url(../assets/img/background.png);
    background-size: cover;
    background-repeat: no-repeat;
}

.main-content {
    display: flex;
    width: 100%;
    height: calc(100vh - 60px);
}

.content {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.content-container {
    width: 100%;
    overflow: hidden;
    padding: 18px;
    margin-top: -150px;

}

.device-table {
    width: 100%;
    border-collapse: collapse;
    color: #000000;
}

.device-table th,
.device-table td {
    border: 1px solid #C9C9C9;
    padding: 8px;
    text-align: center;
    height: 57px;
}

.device-table th {
    background-color: #EEEEEE;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    font-size: 20px;
    line-height: 18px;
    font-style: normal;
    font-weight: normal;
}

.device-table td {
    background-color: #FFFFFF;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    font-size: 16px;
    line-height: 18px;
    font-style: normal;
}

.add-device-btn {
    width: 153px;
    height: 46px;
    border-radius: 0px 10px 0px 10px;
    border: 1px solid #00D7FB;
    background: linear-gradient(90deg, #019CFD 0%, #0A194B 100%);
    font-family: AlibabaPuHuiTi_2_55_Regular;
    font-size: 20px;
    color: #FFFFFF;
    line-height: 36px;
    font-style: normal;
    text-transform: none;
    cursor: pointer;
    /* 悬停时光标样式 */
    transition: transform 0.1s ease, box-shadow 0.2s ease;
    margin-bottom: 20px;
}

.add-device-btn:hover {
    box-shadow: 0 4px 10px rgba(0, 215, 251, 0.4);
    /* 悬停时阴影效果 */
}

.add-device-btn:active {
    transform: scale(0.97);
    /* 点击时稍微缩小按钮 */
    box-shadow: 0 2px 6px rgba(0, 215, 251, 0.6);
    /* 点击时阴影变化 */
}
</style>