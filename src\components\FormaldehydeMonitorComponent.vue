<template>
  <div class="formaldehyde-monitor-container">
    <!-- 头部区域 -->
    <div class="header-section">
      <div class="left-section">
        <div class="realtime-label">实时</div>
        <div class="date-picker-wrapper">
          <el-date-picker
            v-model="selectedDate"
            type="date"
            placeholder="选择日期"
            format="YYYY.MM.DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
            class="custom-date-picker"
          />
        </div>
      </div>
    </div>

    <!-- 数据展示区 -->
    <div class="data-display-section">
      <!-- 左侧可视化图形 -->
      <div class="pyramid-section">
        <div class="pyramid-container">
          <!-- 金字塔四棱锥 -->
          <!-- <div class="pyramid">
            <div class="pyramid-layer layer-green" :class="{ active: currentLevel === '优' }"></div>
            <div class="pyramid-layer layer-yellow" :class="{ active: currentLevel === '良' }"></div>
            <div class="pyramid-layer layer-blue" :class="{ active: currentLevel === '中等' }"></div>
          </div> -->
          <img class="pyramid" src="../assets/img/pyramid.png" alt="" />
          <!-- 等级标签泡泡框 -->
          <div class="level-bubble" :class="`level-${currentLevel}`">
            <div class="bubble-content">{{ currentLevel }}</div>
            <div class="bubble-arrow"></div>
          </div>
        </div>
      </div>

      <!-- 右侧蓝色方块 -->
      <div class="value-block">
        <div class="value-number">{{ formaldehydeValue }}</div>
        <div class="value-label">甲醛</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElDatePicker } from 'element-plus'

// Props
const props = defineProps({
  // 甲醛数值
  formaldehydeValue: {
    type: Number,
    default: 32
  },
  // 初始日期
  initialDate: {
    type: String,
    default: '2025-07-01'
  }
})

// Emits
const emit = defineEmits(['dateChange', 'valueUpdate'])

// 响应式数据
const selectedDate = ref(props.initialDate)

// 等级配置
const levelConfig = [
  { min: 0, max: 30, level: '优', color: '#00E400' },
  { min: 31, max: 60, level: '良', color: '#FFFF00' },
  { min: 61, max: 100, level: '中等', color: '#4A90E2' }
]

// 计算当前等级
const currentLevel = computed(() => {
  const config = levelConfig.find(item => 
    props.formaldehydeValue >= item.min && props.formaldehydeValue <= item.max
  )
  return config ? config.level : '未知'
})

// 处理日期变化
const handleDateChange = (date) => {
  emit('dateChange', date)
}

// 监听props变化
watch(() => props.formaldehydeValue, (newValue) => {
  emit('valueUpdate', newValue)
})

watch(() => props.initialDate, (newDate) => {
  selectedDate.value = newDate
})
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.formaldehyde-monitor-container {
  width: 100%;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// 头部区域
.header-section {
  margin-bottom: 10px;
  padding-bottom: 15px;
}

.left-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.realtime-label {
  color: #333333;
  font-size: 18px;
  font-weight: 600;
}

.date-picker-wrapper {
  display: flex;
  align-items: center;
}

.custom-date-picker {
  width: 150px;
  
  :deep(.el-input__wrapper) {
    background: #ffffff;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    box-shadow: none;

    &:hover {
      border-color: #4A90E2;
    }

    &.is-focus {
      border-color: #4A90E2;
    }
  }

  :deep(.el-input__inner) {
    color: #333333;
    font-size: 14px;

    &::placeholder {
      color: #a8abb2;
    }
  }
}

// 数据展示区
.data-display-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
}

// 左侧金字塔区域
.pyramid-section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pyramid-container {
  position: relative;
  width: 280px;
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pyramid {
  position: relative;
  width: 200px;
  height: 180px;
  transform-style: preserve-3d;
}

.pyramid-layer {
  position: absolute;
  transition: all 0.3s ease;
  opacity: 0.8;

  &.active {
    opacity: 1;
    box-shadow: 0 0 20px currentColor;
  }
}

.layer-green {
  width: 120px;
  height: 60px;
  background: linear-gradient(135deg, #00E400 0%, #00B300 100%);
  bottom: 0;
  left: 15px;
  transform: translateZ(0px);
  clip-path: polygon(20% 0%, 80% 0%, 100% 100%, 0% 100%);
}

.layer-yellow {
  width: 90px;
  height: 45px;
  background: linear-gradient(135deg, #FFFF00 0%, #E6E600 100%);
  bottom: 25px;
  left: 30px;
  transform: translateZ(15px);
  clip-path: polygon(15% 0%, 85% 0%, 100% 100%, 0% 100%);
}

.layer-blue {
  width: 60px;
  height: 30px;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  bottom: 45px;
  left: 45px;
  transform: translateZ(30px);
  clip-path: polygon(10% 0%, 90% 0%, 100% 100%, 0% 100%);
}

// 等级标签泡泡框
.level-bubble {
  position: absolute;
  top: 40%;
  right: -30px;
  transform: translateY(-50%);
  z-index: 10;
  display: flex;
  align-items: center;

  &.level-优 {
    .bubble-content {
      background: #58BF62;
    }
    .bubble-arrow {
      border-right-color: #58BF62;
    }
  }

  &.level-良 {
    .bubble-content {
      background: #FFC700;
    }
    .bubble-arrow {
      border-right-color: #FFC700;
    }
  }

  &.level-中等 {
    .bubble-content {
      background: #1884FD;
    }
    .bubble-arrow {
      border-right-color: #1884FD;
    }
  }
}

.bubble-content {
  text-align: center;
  border-radius: 19px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  min-width: 60px;
  height: 30px;
  white-space: nowrap;
  line-height: 30px;
}

.bubble-arrow {
  position: absolute;
  top: 50%;
  left: -9px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 12px solid;
  border-right-color: inherit;
}

// 右侧数值方块
.value-block {
  background: #369EFD;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  width: 125px;
  height: 125px;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
  top: -90px;
  position: relative;
}

.value-number {
  color: #ffffff;
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 10px;
}

.value-label {
  color: #ffffff;
  font-size: 18px;
  font-weight: 500;
}

// 响应式设计
@media (max-width: 768px) {
  .data-display-section {
    flex-direction: column;
    gap: 30px;
    align-items: center;
  }

  .pyramid-container {
    width: 150px;
    height: 150px;
  }

  .pyramid {
    width: 100px;
    height: 100px;
  }

  .layer-green {
    width: 100px;
    height: 100px;
  }

  .layer-yellow {
    width: 70px;
    height: 70px;
    bottom: 15px;
    left: 15px;
  }

  .layer-blue {
    width: 40px;
    height: 40px;
    bottom: 30px;
    left: 30px;
  }

  .value-number {
    font-size: 36px;
  }

  .value-label {
    font-size: 16px;
  }

  .header-section {
    .left-section {
      flex-direction: column;
      gap: 10px;
      align-items: flex-start;
    }
  }
}
</style>
