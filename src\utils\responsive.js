/**
 * 响应式工具类
 * 基于1920*1080设计稿的响应式适配工具
 */

import { ref, readonly, onMounted, onUnmounted } from 'vue'

// 设计稿基准尺寸
const DESIGN_WIDTH = 1920
const DESIGN_HEIGHT = 1080

/**
 * 将px值转换为vw
 * @param {number} px - 像素值
 * @param {number} designWidth - 设计稿宽度，默认1920
 * @returns {string} vw值
 */
export function pxToVw(px, designWidth = DESIGN_WIDTH) {
  return `${(px / designWidth * 100).toFixed(5)}vw`
}

/**
 * 将px值转换为vh
 * @param {number} px - 像素值
 * @param {number} designHeight - 设计稿高度，默认1080
 * @returns {string} vh值
 */
export function pxToVh(px, designHeight = DESIGN_HEIGHT) {
  return `${(px / designHeight * 100).toFixed(5)}vh`
}

/**
 * 获取当前屏幕相对于设计稿的缩放比例
 * @returns {object} 包含宽度和高度缩放比例的对象
 */
export function getScaleRatio() {
  const screenWidth = window.innerWidth
  const screenHeight = window.innerHeight

  return {
    widthRatio: screenWidth / DESIGN_WIDTH,
    heightRatio: screenHeight / DESIGN_HEIGHT,
    minRatio: Math.min(screenWidth / DESIGN_WIDTH, screenHeight / DESIGN_HEIGHT)
  }
}

/**
 * 根据屏幕尺寸动态计算字体大小
 * @param {number} baseFontSize - 基础字体大小(px)
 * @param {number} minSize - 最小字体大小(px)
 * @param {number} maxSize - 最大字体大小(px)
 * @returns {string} 计算后的字体大小
 */
export function getResponsiveFontSize(baseFontSize, minSize = 12, maxSize = 48) {
  const { widthRatio } = getScaleRatio()
  const calculatedSize = baseFontSize * widthRatio

  // 限制字体大小在合理范围内
  const finalSize = Math.max(minSize, Math.min(maxSize, calculatedSize))
  return `${finalSize}px`
}

/**
 * 检测当前屏幕类型
 * @returns {string} 屏幕类型标识
 */
export function getScreenType() {
  const width = window.innerWidth

  if (width >= 3840) return 'desktop4k'      // 4K显示器
  if (width >= 2560) return 'desktop2k'      // 2K显示器
  if (width >= 1920) return 'desktop'        // 标准桌面
  if (width >= 1366) return 'laptop'         // 笔记本
  if (width >= 1024) return 'tablet'         // 平板
  return 'mobile'                            // 移动设备
}

/**
 * 创建响应式样式对象
 * @param {object} styles - 样式对象，值为px数值
 * @returns {object} 转换后的样式对象
 */
export function createResponsiveStyles(styles) {
  const responsiveStyles = {}

  for (const [key, value] of Object.entries(styles)) {
    if (typeof value === 'number') {
      // 对于数值，转换为vw
      responsiveStyles[key] = pxToVw(value)
    } else if (typeof value === 'string' && value.includes('px')) {
      // 对于包含px的字符串，提取数值并转换
      const pxValue = parseFloat(value)
      if (!isNaN(pxValue)) {
        responsiveStyles[key] = value.replace(/\d+(\.\d+)?px/, pxToVw(pxValue))
      } else {
        responsiveStyles[key] = value
      }
    } else {
      // 其他情况保持原值
      responsiveStyles[key] = value
    }
  }

  return responsiveStyles
}

/**
 * 监听屏幕尺寸变化
 * @param {function} callback - 回调函数
 * @returns {function} 取消监听的函数
 */
export function onScreenResize(callback) {
  const handleResize = () => {
    callback({
      width: window.innerWidth,
      height: window.innerHeight,
      screenType: getScreenType(),
      scaleRatio: getScaleRatio()
    })
  }

  window.addEventListener('resize', handleResize)

  // 立即执行一次
  handleResize()

  // 返回取消监听的函数
  return () => {
    window.removeEventListener('resize', handleResize)
  }
}

/**
 * Vue组合式API：响应式屏幕信息
 * @returns {object} 响应式的屏幕信息
 */
export function useResponsive() {
  const screenInfo = ref({
    width: window.innerWidth,
    height: window.innerHeight,
    screenType: getScreenType(),
    scaleRatio: getScaleRatio()
  })

  const updateScreenInfo = () => {
    screenInfo.value = {
      width: window.innerWidth,
      height: window.innerHeight,
      screenType: getScreenType(),
      scaleRatio: getScaleRatio()
    }
  }

  onMounted(() => {
    window.addEventListener('resize', updateScreenInfo)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateScreenInfo)
  })

  return {
    screenInfo: readonly(screenInfo),
    pxToVw,
    pxToVh,
    getResponsiveFontSize,
    createResponsiveStyles
  }
}

// 导出常量
export { DESIGN_WIDTH, DESIGN_HEIGHT }
